import 'dart:io';

import 'Dog.dart';
import 'lion.dart';
import 'animal.dart';

void main(){
//type casting
//print("enter value of num1:");
//int num1=int.parse(stdin.readLineSync()!);
//print("enter value of num2:");
//int num2=int.parse(stdin.readLineSync()!);
//int result=num1+num2;
//print("result=$result");
//Map <String,int> saleries={'nihad':21,'rohit':22};
//print(saleries['nihad']);
//poo 
// nihad = Human(1.53, 54,12);
//ihad.age=20;
//print(nihad.age);
Dog jack=Dog();
lion simba=lion();
jack.bark();
simba.roar();
jack.eat();
simba.eat();




}
